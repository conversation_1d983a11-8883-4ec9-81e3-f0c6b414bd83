from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status
from typing import Dict

from app.core.hybrid_encryption import hybrid_encryption
from app.schemas.encryption import (
    EncryptionRequest,
    EncryptionResponse,
    DecryptionRequest,
    DecryptionResponse,
    PublicKeyResponse,
    KeyPairResponse,
    BulkEncryptionRequest,
    BulkEncryptionResponse,
    BulkDecryptionRequest,
    BulkDecryptionResponse
)

router = APIRouter(prefix="/encryption", tags=["encryption"])

@router.get("/public-key", response_model=PublicKeyResponse)
async def get_public_key():
    """Get the server's public key for encryption"""
    try:
        # Create a fresh instance to avoid global state issues
        from app.core.hybrid_encryption import HybridEncryption
        encryption_instance = HybridEncryption()
        public_key_pem = encryption_instance.get_public_key_pem()
        return PublicKeyResponse(public_key_pem=public_key_pem)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get public key: {str(e)}"
        )

@router.post("/generate-keypair", response_model=KeyPairResponse)
async def generate_key_pair():
    """Generate a new RSA key pair"""
    try:
        # Create a fresh instance to avoid global state issues
        from app.core.hybrid_encryption import HybridEncryption
        encryption_instance = HybridEncryption()
        private_key_pem, public_key_pem = encryption_instance.generate_key_pair()
        return KeyPairResponse(
            private_key_pem=private_key_pem,
            public_key_pem=public_key_pem
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate key pair: {str(e)}"
        )

@router.post("/encrypt", response_model=EncryptionResponse)
async def encrypt_data(request: EncryptionRequest):
    """Encrypt data using hybrid encryption"""
    try:
        encrypted_package = hybrid_encryption.encrypt_data(
            data=request.data,
            public_key_pem=request.public_key_pem
        )
        
        return EncryptionResponse(**encrypted_package)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Encryption failed: {str(e)}"
        )

@router.post("/decrypt", response_model=DecryptionResponse)
async def decrypt_data(request: DecryptionRequest):
    """Decrypt data using hybrid decryption"""
    try:
        encrypted_package = {
            "encrypted_data": request.encrypted_data,
            "encrypted_key": request.encrypted_key,
            "iv": request.iv,
            "algorithm": request.algorithm,
            "key_algorithm": request.key_algorithm
        }
        
        decrypted_data = hybrid_encryption.decrypt_data(
            encrypted_package=encrypted_package,
            private_key_pem=request.private_key_pem
        )
        
        return DecryptionResponse(data=decrypted_data)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Decryption failed: {str(e)}"
        )

@router.post("/encrypt-bulk", response_model=BulkEncryptionResponse)
async def encrypt_bulk_data(request: BulkEncryptionRequest):
    """Encrypt multiple data items at once"""
    try:
        encrypted_items = {}
        
        for key, data in request.data_items.items():
            encrypted_package = hybrid_encryption.encrypt_data(
                data=data,
                public_key_pem=request.public_key_pem
            )
            encrypted_items[key] = EncryptionResponse(**encrypted_package)
        
        return BulkEncryptionResponse(encrypted_items=encrypted_items)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk encryption failed: {str(e)}"
        )

@router.post("/decrypt-bulk", response_model=BulkDecryptionResponse)
async def decrypt_bulk_data(request: BulkDecryptionRequest):
    """Decrypt multiple data items at once"""
    try:
        decrypted_items = {}
        
        for key, encrypted_item in request.encrypted_items.items():
            encrypted_package = {
                "encrypted_data": encrypted_item.encrypted_data,
                "encrypted_key": encrypted_item.encrypted_key,
                "iv": encrypted_item.iv,
                "algorithm": encrypted_item.algorithm,
                "key_algorithm": encrypted_item.key_algorithm
            }
            
            decrypted_data = hybrid_encryption.decrypt_data(
                encrypted_package=encrypted_package,
                private_key_pem=request.private_key_pem
            )
            decrypted_items[key] = decrypted_data
        
        return BulkDecryptionResponse(decrypted_items=decrypted_items)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Bulk decryption failed: {str(e)}"
        )
