import json
import logging
from typing import Any, Optional, Union

# Redis imports with error handling for IDE compatibility
try:
    import redis.asyncio as aioredis
    from redis.exceptions import RedisError, ConnectionError
    REDIS_AVAILABLE = True
except ImportError as e:
    # Fallback for development/IDE environments
    print(f"Warning: Redis import failed: {e}")
    aioredis = None
    RedisError = Exception
    ConnectionError = Exception
    REDIS_AVAILABLE = False

from app.core.config import settings

logger = logging.getLogger(__name__)

class RedisCache:
    """Redis cache service for auth-service"""
    
    def __init__(self):
        self.redis_client: Optional[Any] = None
        self.enabled = settings.ENABLE_CACHE
        
    async def connect(self) -> bool:
        """Initialize Redis connection"""
        if not self.enabled:
            logger.info("Redis cache is disabled")
            return False

        if not REDIS_AVAILABLE or aioredis is None:
            logger.error("Redis package not available - cache will be disabled")
            return False

        try:
            # Create Redis connection
            if settings.REDIS_PASSWORD:
                self.redis_client = aioredis.from_url(
                    f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}",
                    encoding="utf-8",
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            else:
                self.redis_client = aioredis.Redis(
                    host=settings.REDIS_HOST,
                    port=settings.REDIS_PORT,
                    db=settings.REDIS_DB,
                    encoding="utf-8",
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
            
            # Test connection
            await self.redis_client.ping()
            logger.info(f"Connected to Redis at {settings.REDIS_HOST}:{settings.REDIS_PORT}")
            return True
            
        except (ConnectionError, RedisError) as e:
            logger.error(f"Failed to connect to Redis: {e}")
            self.redis_client = None
            return False
    
    async def disconnect(self):
        """Close Redis connection"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            logger.info("Disconnected from Redis")
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if not self.enabled or not self.redis_client:
            return None
            
        try:
            value = await self.redis_client.get(key)
            if value:
                # Try to parse as JSON, fallback to string
                try:
                    return json.loads(value)
                except json.JSONDecodeError:
                    return value
            return None
        except RedisError as e:
            logger.error(f"Redis GET error for key {key}: {e}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache"""
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            # Serialize value to JSON if it's not a string
            if isinstance(value, (dict, list, tuple)):
                value = json.dumps(value)
            elif not isinstance(value, str):
                value = str(value)
            
            ttl = ttl or settings.CACHE_TTL
            await self.redis_client.setex(key, ttl, value)
            return True
        except RedisError as e:
            logger.error(f"Redis SET error for key {key}: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """Delete key from cache"""
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            result = await self.redis_client.delete(key)
            return result > 0
        except RedisError as e:
            logger.error(f"Redis DELETE error for key {key}: {e}")
            return False
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        if not self.enabled or not self.redis_client:
            return False
            
        try:
            result = await self.redis_client.exists(key)
            return result > 0
        except RedisError as e:
            logger.error(f"Redis EXISTS error for key {key}: {e}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """Clear all keys matching pattern"""
        if not self.enabled or not self.redis_client:
            return 0
            
        try:
            keys = await self.redis_client.keys(pattern)
            if keys:
                return await self.redis_client.delete(*keys)
            return 0
        except RedisError as e:
            logger.error(f"Redis CLEAR_PATTERN error for pattern {pattern}: {e}")
            return 0
    
    async def health_check(self) -> dict:
        """Check Redis health status"""
        if not self.enabled:
            return {"status": "disabled", "message": "Redis cache is disabled"}
            
        if not self.redis_client:
            return {"status": "disconnected", "message": "Redis client not initialized"}
            
        try:
            await self.redis_client.ping()
            info = await self.redis_client.info()
            return {
                "status": "healthy",
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "unknown"),
                "redis_version": info.get("redis_version", "unknown")
            }
        except RedisError as e:
            return {"status": "unhealthy", "error": str(e)}

# Global cache instance
cache = RedisCache()

# Cache key generators
def get_user_cache_key(user_id: int) -> str:
    """Generate cache key for user data"""
    return f"user:{user_id}"

def get_user_email_cache_key(email: str) -> str:
    """Generate cache key for user lookup by email"""
    return f"user:email:{email}"

def get_token_cache_key(token: str) -> str:
    """Generate cache key for token validation"""
    return f"token:{token[:20]}"  # Use first 20 chars to avoid very long keys

def get_login_token_cache_key(user_id: int) -> str:
    """Generate cache key for user login token"""
    return f"login_token:{user_id}"
