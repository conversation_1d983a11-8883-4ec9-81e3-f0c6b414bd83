import os
from typing import List, Optional, Union
from pydantic import AnyHttpUrl, field_validator, computed_field
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    PROJECT_NAME: str = "ZCare Auth Service"
    API_V1_STR: str = "/api/v1"

    # CORS settings
    CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:9080",  # APISIX Gateway
        "http://localhost:8002"   # Direct auth-service access
    ]

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # Database settings
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "Arunnathan")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "auth_service")

    @computed_field
    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}/{self.POSTGRES_DB}"

    # JWT settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "supersecretkey")
    ALGORITHM: str = "HS256"
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 10080  # 7 days

    # Email settings
    MAIL_USERNAME: str = os.getenv("MAIL_USERNAME", "")
    MAIL_PASSWORD: str = os.getenv("MAIL_PASSWORD", "")
    MAIL_FROM: str = os.getenv("MAIL_FROM", "<EMAIL>")
    MAIL_PORT: int = int(os.getenv("MAIL_PORT", "587"))
    MAIL_SERVER: str = os.getenv("MAIL_SERVER", "smtp.gmail.com")
    MAIL_FROM_NAME: str = os.getenv("MAIL_FROM_NAME", "Zionix Auth Service")

    model_config = {
        "case_sensitive": True,
        "env_file": ".env"
    }

settings = Settings()

class VaultConfig:
    @staticmethod
    def _read_key_file(path: str) -> str:
        try:
            with open(path, "r") as f:
                return f.read().replace('\r\n', '\n')
        except FileNotFoundError:
            return ""

# Initialize the keys after the class is defined
VaultConfig.PRIVATE_KEY = VaultConfig._read_key_file("private.pem")
VaultConfig.PUBLIC_KEY = VaultConfig._read_key_file("public.pem")
