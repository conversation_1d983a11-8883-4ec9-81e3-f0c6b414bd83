import os
import base64
import j<PERSON>
from typing import <PERSON><PERSON>, <PERSON><PERSON>, Optional
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.backends import default_backend
import logging

logger = logging.getLogger(__name__)

class HybridEncryption:
    """
    Hybrid encryption service that combines RSA (asymmetric) and AES (symmetric) encryption.
    
    - RSA is used for key exchange and small data encryption
    - AES is used for bulk data encryption (faster and more efficient)
    - This provides the security of RSA with the performance of AES
    """
    
    def __init__(self):
        self.backend = default_backend()
        self._private_key = None
        self._public_key = None
        self._load_or_generate_keys()
    
    def _load_or_generate_keys(self):
        """Load existing RSA keys or generate new ones"""
        try:
            # Try to load existing keys from environment or files
            private_key_pem = os.getenv("RSA_PRIVATE_KEY")
            public_key_pem = os.getenv("RSA_PUBLIC_KEY")
            
            if private_key_pem and public_key_pem:
                self._private_key = serialization.load_pem_private_key(
                    private_key_pem.encode(),
                    password=None,
                    backend=self.backend
                )
                self._public_key = serialization.load_pem_public_key(
                    public_key_pem.encode(),
                    backend=self.backend
                )
                logger.info("Loaded RSA keys from environment")
            else:
                # Generate new keys
                self._generate_rsa_keys()
                logger.info("Generated new RSA keys")
                
        except Exception as e:
            logger.warning(f"Failed to load keys: {e}. Generating new keys.")
            self._generate_rsa_keys()
    
    def _generate_rsa_keys(self):
        """Generate new RSA key pair"""
        self._private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
            backend=self.backend
        )
        self._public_key = self._private_key.public_key()
    
    def get_public_key_pem(self) -> str:
        """Get the public key in PEM format for sharing with clients"""
        pem = self._public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )
        return pem.decode('utf-8')
    
    def get_private_key_pem(self) -> str:
        """Get the private key in PEM format (for backup/storage)"""
        pem = self._private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        )
        return pem.decode('utf-8')
    
    def _generate_aes_key(self) -> bytes:
        """Generate a random AES-256 key"""
        return os.urandom(32)  # 256 bits
    
    def _generate_iv(self) -> bytes:
        """Generate a random initialization vector for AES"""
        return os.urandom(16)  # 128 bits
    
    def encrypt_data(self, data: str, public_key_pem: Optional[str] = None) -> Dict[str, str]:
        """
        Encrypt data using hybrid encryption
        
        Args:
            data: The data to encrypt
            public_key_pem: Optional public key PEM. If not provided, uses server's public key
            
        Returns:
            Dictionary containing encrypted data and metadata
        """
        try:
            # Use provided public key or server's public key
            if public_key_pem:
                public_key = serialization.load_pem_public_key(
                    public_key_pem.encode(),
                    backend=self.backend
                )
            else:
                public_key = self._public_key
            
            # Step 1: Generate AES key and IV
            aes_key = self._generate_aes_key()
            iv = self._generate_iv()
            
            # Step 2: Encrypt data with AES
            cipher = Cipher(
                algorithms.AES(aes_key),
                modes.CBC(iv),
                backend=self.backend
            )
            encryptor = cipher.encryptor()
            
            # Pad data to be multiple of 16 bytes (AES block size)
            data_bytes = data.encode('utf-8')
            padding_length = 16 - (len(data_bytes) % 16)
            padded_data = data_bytes + bytes([padding_length] * padding_length)
            
            encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
            
            # Step 3: Encrypt AES key with RSA
            encrypted_aes_key = public_key.encrypt(
                aes_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Step 4: Return base64 encoded results
            return {
                "encrypted_data": base64.b64encode(encrypted_data).decode('utf-8'),
                "encrypted_key": base64.b64encode(encrypted_aes_key).decode('utf-8'),
                "iv": base64.b64encode(iv).decode('utf-8'),
                "algorithm": "AES-256-CBC",
                "key_algorithm": "RSA-OAEP"
            }
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise ValueError(f"Encryption failed: {e}")
    
    def decrypt_data(self, encrypted_package: Dict[str, str], private_key_pem: Optional[str] = None) -> str:
        """
        Decrypt data using hybrid decryption
        
        Args:
            encrypted_package: Dictionary containing encrypted data and metadata
            private_key_pem: Optional private key PEM. If not provided, uses server's private key
            
        Returns:
            Decrypted data as string
        """
        try:
            # Use provided private key or server's private key
            if private_key_pem:
                private_key = serialization.load_pem_private_key(
                    private_key_pem.encode(),
                    password=None,
                    backend=self.backend
                )
            else:
                private_key = self._private_key
            
            # Step 1: Decode base64 data
            encrypted_data = base64.b64decode(encrypted_package["encrypted_data"])
            encrypted_aes_key = base64.b64decode(encrypted_package["encrypted_key"])
            iv = base64.b64decode(encrypted_package["iv"])
            
            # Step 2: Decrypt AES key with RSA
            aes_key = private_key.decrypt(
                encrypted_aes_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Step 3: Decrypt data with AES
            cipher = Cipher(
                algorithms.AES(aes_key),
                modes.CBC(iv),
                backend=self.backend
            )
            decryptor = cipher.decryptor()
            
            padded_data = decryptor.update(encrypted_data) + decryptor.finalize()
            
            # Step 4: Remove padding
            padding_length = padded_data[-1]
            data = padded_data[:-padding_length]
            
            return data.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise ValueError(f"Decryption failed: {e}")
    
    def encrypt_sensitive_field(self, field_value: str) -> str:
        """
        Encrypt a sensitive field and return as JSON string
        
        Args:
            field_value: The sensitive data to encrypt
            
        Returns:
            JSON string containing encrypted data
        """
        encrypted_package = self.encrypt_data(field_value)
        return json.dumps(encrypted_package)
    
    def decrypt_sensitive_field(self, encrypted_json: str) -> str:
        """
        Decrypt a sensitive field from JSON string
        
        Args:
            encrypted_json: JSON string containing encrypted data
            
        Returns:
            Decrypted field value
        """
        encrypted_package = json.loads(encrypted_json)
        return self.decrypt_data(encrypted_package)

# Global instance
hybrid_encryption = HybridEncryption()
