"""update users table schema

Revision ID: 002
Revises: 001
Create Date: 2024-01-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '002'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade():
    # Drop old columns
    op.drop_index('ix_users_username', table_name='users')
    op.drop_column('users', 'username')
    op.drop_column('users', 'full_name')
    
    # Add new columns
    op.add_column('users', sa.Column('first_name', sa.String(length=50), nullable=False))
    op.add_column('users', sa.Column('last_name', sa.String(length=50), nullable=False))


def downgrade():
    # Drop new columns
    op.drop_column('users', 'last_name')
    op.drop_column('users', 'first_name')
    
    # Add old columns back
    op.add_column('users', sa.Column('username', sa.String(length=50), nullable=False))
    op.add_column('users', sa.Column('full_name', sa.String(length=100), nullable=True))
    op.create_index('ix_users_username', 'users', ['username'], unique=True)
