"""add encrypted fields to users table

Revision ID: 003
Revises: 002
Create Date: 2024-01-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '003'
down_revision = '002'
branch_labels = None
depends_on = None


def upgrade():
    # Add encrypted fields to users table
    op.add_column('users', sa.Column('encrypted_phone', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('encrypted_address', sa.Text(), nullable=True))
    op.add_column('users', sa.Column('encrypted_notes', sa.Text(), nullable=True))


def downgrade():
    # Remove encrypted fields from users table
    op.drop_column('users', 'encrypted_notes')
    op.drop_column('users', 'encrypted_address')
    op.drop_column('users', 'encrypted_phone')
