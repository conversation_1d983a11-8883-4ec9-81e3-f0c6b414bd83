import json
import logging
import asyncio
from typing import Dict, Any

from app.events.kafka_client import get_consumer

logger = logging.getLogger(__name__)

class UserEventsConsumer:
    """Consumer for user events from other services"""
    
    def __init__(self):
        self.consumer = None
        self.running = False
    
    async def start(self):
        """Start the consumer"""
        self.consumer = await get_consumer("user-events")
        await self.consumer.start()
        self.running = True
        logger.info("User events consumer started")
    
    async def stop(self):
        """Stop the consumer"""
        self.running = False
        if self.consumer:
            await self.consumer.stop()
        logger.info("User events consumer stopped")
    
    async def consume_messages(self):
        """Consume messages from the user-events topic"""
        if not self.consumer:
            logger.error("Consumer not initialized. Call start() first.")
            return
        
        while self.running:
            try:
                # Get messages from Kafka
                message_batch = await self.consumer.getmany(timeout_ms=1000)
                
                for topic_partition, messages in message_batch.items():
                    for message in messages:
                        await self.process_message(message)
                        
            except Exception as e:
                logger.error(f"Error consuming messages: {str(e)}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def process_message(self, message):
        """Process a single message"""
        try:
            # Decode the message
            event_data = json.loads(message.value.decode('utf-8'))
            event_type = event_data.get('event_type')
            
            logger.info(f"Processing event: {event_type}")
            
            # Route to appropriate handler based on event type
            if event_type == "user_created":
                await self.handle_user_created(event_data)
            elif event_type == "user_updated":
                await self.handle_user_updated(event_data)
            elif event_type == "user_deleted":
                await self.handle_user_deleted(event_data)
            elif event_type == "user_login":
                await self.handle_user_login(event_data)
            elif event_type == "user_logout":
                await self.handle_user_logout(event_data)
            elif event_type == "user_password_changed":
                await self.handle_user_password_changed(event_data)
            else:
                logger.warning(f"Unknown event type: {event_type}")
                
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
    
    async def handle_user_created(self, event_data: Dict[str, Any]):
        """Handle user created event"""
        user_id = event_data.get('user_id')
        email = event_data.get('email')
        logger.info(f"User created: {user_id} ({email})")
        # Add your business logic here
        # For example: send welcome email, create user profile in other services, etc.
    
    async def handle_user_updated(self, event_data: Dict[str, Any]):
        """Handle user updated event"""
        user_id = event_data.get('user_id')
        updated_fields = event_data.get('updated_fields', [])
        logger.info(f"User updated: {user_id}, fields: {updated_fields}")
        # Add your business logic here
        # For example: sync user data with other services
    
    async def handle_user_deleted(self, event_data: Dict[str, Any]):
        """Handle user deleted event"""
        user_id = event_data.get('user_id')
        email = event_data.get('email')
        logger.info(f"User deleted: {user_id} ({email})")
        # Add your business logic here
        # For example: cleanup user data in other services
    
    async def handle_user_login(self, event_data: Dict[str, Any]):
        """Handle user login event"""
        user_id = event_data.get('user_id')
        email = event_data.get('email')
        ip_address = event_data.get('ip_address')
        logger.info(f"User login: {user_id} ({email}) from {ip_address}")
        # Add your business logic here
        # For example: log security events, update last login time
    
    async def handle_user_logout(self, event_data: Dict[str, Any]):
        """Handle user logout event"""
        user_id = event_data.get('user_id')
        email = event_data.get('email')
        session_duration = event_data.get('session_duration')
        logger.info(f"User logout: {user_id} ({email}), session duration: {session_duration}s")
        # Add your business logic here
        # For example: log session analytics
    
    async def handle_user_password_changed(self, event_data: Dict[str, Any]):
        """Handle user password changed event"""
        user_id = event_data.get('user_id')
        email = event_data.get('email')
        logger.info(f"User password changed: {user_id} ({email})")
        # Add your business logic here
        # For example: send security notification email

# Global consumer instance
user_events_consumer = UserEventsConsumer()

async def start_user_events_consumer():
    """Start the user events consumer"""
    await user_events_consumer.start()
    await user_events_consumer.consume_messages()

async def stop_user_events_consumer():
    """Stop the user events consumer"""
    await user_events_consumer.stop()
