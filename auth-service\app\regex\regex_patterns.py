# Regex patterns for user validation

# First Name (Only letters, at least 2 characters)
AUTH_FIRST_NAME_PATTERN = r"^[A-Za-z]{2,}$"

# Last Name (Only letters, at least 2 characters)
AUTH_LAST_NAME_PATTERN = r"^[A-Za-z]{2,}$"

# Email Address (Standard email format)
AUTH_EMAIL_PATTERN = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"

# Password (Min 8 chars, allows letters, numbers, and special chars)
# Note: Complex validation (uppercase, lowercase, number, special char) will be done in validator
AUTH_PASSWORD_PATTERN = r"^[A-Za-z\d@$!%*?&]{8,}$"

# Confirm Password (Checked programmatically; same as password)
AUTH_CONFIRM_PASSWORD_PATTERN = AUTH_PASSWORD_PATTERN
