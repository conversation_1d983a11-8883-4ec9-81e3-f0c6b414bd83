#!/usr/bin/env python3
"""
Debug script to identify startup issues step by step
"""

import sys
import os
import traceback

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def step_1_basic_imports():
    """Test basic imports"""
    print("Step 1: Testing basic imports...")
    try:
        from fastapi import FastAPI
        from fastapi.middleware.cors import CORSMiddleware
        from contextlib import asynccontextmanager
        print("✓ Basic FastAPI imports successful")
        return True
    except Exception as e:
        print(f"✗ Basic imports failed: {e}")
        traceback.print_exc()
        return False

def step_2_config_import():
    """Test config import"""
    print("\nStep 2: Testing config import...")
    try:
        from app.core.config import settings
        print(f"✓ Config imported successfully")
        print(f"  - Project name: {settings.PROJECT_NAME}")
        print(f"  - API prefix: {settings.API_V1_STR}")
        print(f"  - Database URI: {settings.SQLALCHEMY_DATABASE_URI[:50]}...")
        return True
    except Exception as e:
        print(f"✗ Config import failed: {e}")
        traceback.print_exc()
        return False

def step_3_database_import():
    """Test database imports"""
    print("\nStep 3: Testing database imports...")
    try:
        from app.db.session import engine, Base
        from app.db.base import Base as ImportedBase
        print("✓ Database imports successful")
        return True
    except Exception as e:
        print(f"✗ Database imports failed: {e}")
        traceback.print_exc()
        return False

def step_4_kafka_import():
    """Test Kafka imports"""
    print("\nStep 4: Testing Kafka imports...")
    try:
        from app.events.kafka_client import KAFKA_ENABLED
        print(f"✓ Kafka imports successful")
        print(f"  - Kafka enabled: {KAFKA_ENABLED}")
        return True
    except Exception as e:
        print(f"✗ Kafka imports failed: {e}")
        traceback.print_exc()
        return False

def step_5_routes_import():
    """Test route imports"""
    print("\nStep 5: Testing route imports...")
    try:
        from app.api.routes import auth, users, encryption
        print("✓ Route imports successful")
        return True
    except Exception as e:
        print(f"✗ Route imports failed: {e}")
        traceback.print_exc()
        return False

def step_6_create_app():
    """Test FastAPI app creation"""
    print("\nStep 6: Testing FastAPI app creation...")
    try:
        from fastapi import FastAPI
        from contextlib import asynccontextmanager
        from app.core.config import settings
        from app.events.kafka_client import KAFKA_ENABLED
        
        @asynccontextmanager
        async def lifespan(app: FastAPI):
            print("Lifespan startup...")
            yield
            print("Lifespan shutdown...")
        
        app = FastAPI(
            title=settings.PROJECT_NAME,
            description="Authentication Service API for ZCare Platform",
            version="0.1.0",
            docs_url="/docs",
            redoc_url="/redoc",
            lifespan=lifespan
        )
        print("✓ FastAPI app created successfully")
        return True, app
    except Exception as e:
        print(f"✗ FastAPI app creation failed: {e}")
        traceback.print_exc()
        return False, None

def step_7_add_middleware(app):
    """Test middleware addition"""
    print("\nStep 7: Testing middleware addition...")
    try:
        from fastapi.middleware.cors import CORSMiddleware
        from app.core.config import settings
        
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.CORS_ORIGINS if settings.CORS_ORIGINS else ["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        print("✓ Middleware added successfully")
        return True
    except Exception as e:
        print(f"✗ Middleware addition failed: {e}")
        traceback.print_exc()
        return False

def step_8_add_routes(app):
    """Test route addition"""
    print("\nStep 8: Testing route addition...")
    try:
        from app.api.routes import auth, users, encryption
        from app.core.config import settings
        
        app.include_router(auth.router, prefix=settings.API_V1_STR)
        app.include_router(users.router, prefix=settings.API_V1_STR)
        app.include_router(encryption.router, prefix=settings.API_V1_STR)
        print("✓ Routes added successfully")
        return True
    except Exception as e:
        print(f"✗ Route addition failed: {e}")
        traceback.print_exc()
        return False

def step_9_test_openapi(app):
    """Test OpenAPI schema generation"""
    print("\nStep 9: Testing OpenAPI schema generation...")
    try:
        schema = app.openapi()
        print("✓ OpenAPI schema generated successfully")
        print(f"  - Paths: {len(schema.get('paths', {}))}")
        return True
    except Exception as e:
        print(f"✗ OpenAPI schema generation failed: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("FastAPI Startup Debug Script")
    print("=" * 60)
    
    steps = [
        ("Basic Imports", step_1_basic_imports, []),
        ("Config Import", step_2_config_import, []),
        ("Database Import", step_3_database_import, []),
        ("Kafka Import", step_4_kafka_import, []),
        ("Routes Import", step_5_routes_import, []),
        ("Create App", step_6_create_app, []),
        ("Add Middleware", step_7_add_middleware, ["app"]),
        ("Add Routes", step_8_add_routes, ["app"]),
        ("Test OpenAPI", step_9_test_openapi, ["app"]),
    ]
    
    app = None
    failed_step = None
    
    for step_name, step_func, args in steps:
        print(f"\n{'='*20}")
        try:
            if args:
                if "app" in args and app is None:
                    print(f"Skipping {step_name} - app not available")
                    continue
                if step_name == "Create App":
                    success, app = step_func()
                else:
                    success = step_func(app)
            else:
                success = step_func()
            
            if not success:
                failed_step = step_name
                break
                
        except Exception as e:
            print(f"✗ Unexpected error in {step_name}: {e}")
            traceback.print_exc()
            failed_step = step_name
            break
    
    print(f"\n{'='*60}")
    print("Debug Results:")
    print("=" * 60)
    
    if failed_step:
        print(f"✗ Failed at step: {failed_step}")
        print("The issue is likely in this step or its dependencies.")
    else:
        print("✓ All steps completed successfully!")
        print("The FastAPI app should work correctly.")
        print("\nTo start the server:")
        print("  python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload")
        print("\nSwagger UI should be available at: http://localhost:8001/docs")