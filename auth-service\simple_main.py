#!/usr/bin/env python3
"""
Simplified main.py to test Swagger UI without complex dependencies
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

# Simple FastAPI app without complex dependencies
app = FastAPI(
    title="ZCare Auth Service",
    description="Authentication Service API for ZCare Platform",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {"message": "Welcome to ZCare Auth Service"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/test")
async def test_endpoint():
    return {"message": "Test endpoint working", "status": "success"}

if __name__ == "__main__":
    import uvicorn
    print("Starting simplified FastAPI server...")
    print("Swagger UI will be available at: http://localhost:8001/docs")
    print("ReDoc will be available at: http://localhost:8001/redoc")
    uvicorn.run("simple_main:app", host="0.0.0.0", port=8001, reload=True)