#!/usr/bin/env python3
"""
Test script to check if the FastAPI app can be imported and started
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """Test if we can import the FastAPI app"""
    try:
        print("Testing FastAPI app import...")
        from app.main import app
        print("✓ FastAPI app imported successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to import FastAPI app: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_openapi():
    """Test if OpenAPI schema can be generated"""
    try:
        print("Testing OpenAPI schema generation...")
        from app.main import app
        schema = app.openapi()
        print("✓ OpenAPI schema generated successfully")
        print(f"  - Title: {schema.get('info', {}).get('title', 'N/A')}")
        print(f"  - Version: {schema.get('info', {}).get('version', 'N/A')}")
        print(f"  - Paths count: {len(schema.get('paths', {}))}")
        return True
    except Exception as e:
        print(f"✗ Failed to generate OpenAPI schema: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_routes():
    """Test if routes are properly registered"""
    try:
        print("Testing route registration...")
        from app.main import app
        routes = [route.path for route in app.routes]
        print(f"✓ Found {len(routes)} routes:")
        for route in routes:
            print(f"  - {route}")
        return True
    except Exception as e:
        print(f"✗ Failed to get routes: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("FastAPI App Diagnostic Test")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_import),
        ("OpenAPI Test", test_openapi),
        ("Routes Test", test_routes),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 20)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print("=" * 50)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n✓ All tests passed! The FastAPI app should work correctly.")
        print("\nTo start the server, run:")
        print("  python -m uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload")
        print("\nThen access Swagger UI at: http://localhost:8001/docs")
    else:
        print("\n✗ Some tests failed. Please check the errors above.")
    
    sys.exit(0 if all_passed else 1)