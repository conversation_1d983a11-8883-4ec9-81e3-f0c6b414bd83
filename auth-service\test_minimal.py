#!/usr/bin/env python3
"""
Minimal FastAPI app to test if Swagger UI works
"""

from fastapi import FastAPI

app = FastAPI(
    title="Test Auth Service",
    description="Minimal test for Swagger UI",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

@app.get("/")
async def root():
    return {"message": "Hello World"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("test_minimal:app", host="0.0.0.0", port=8001, reload=True)