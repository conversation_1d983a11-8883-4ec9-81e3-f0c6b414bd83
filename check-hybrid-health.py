#!/usr/bin/env python3
"""
ZCare Hybrid Gateway Health Check Script
Monitors APISIX (Auth) + Envoy (Admin) + All Services
"""

import requests
import json
import time
import sys
from datetime import datetime
import subprocess

class Colors:
    GREEN = '\033[92m'
    RED = '\033[91m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

def print_header():
    print(f"{Colors.CYAN}{Colors.BOLD}")
    print("=" * 70)
    print("🏥 ZCare Hybrid Gateway Health Check")
    print("=" * 70)
    print(f"{Colors.END}")
    print(f"{Colors.WHITE}Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}{Colors.END}")
    print()

def check_service(url, name, timeout=10):
    """Check if a service is healthy"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ {name:<25} - Healthy{Colors.END}")
            return True, response.json() if 'application/json' in response.headers.get('content-type', '') else response.text
        else:
            print(f"{Colors.RED}❌ {name:<25} - HTTP {response.status_code}{Colors.END}")
            return False, None
    except requests.exceptions.ConnectionError:
        print(f"{Colors.RED}❌ {name:<25} - Connection refused{Colors.END}")
        return False, None
    except requests.exceptions.Timeout:
        print(f"{Colors.YELLOW}⚠️  {name:<25} - Timeout{Colors.END}")
        return False, None
    except Exception as e:
        print(f"{Colors.RED}❌ {name:<25} - Error: {str(e)}{Colors.END}")
        return False, None

def check_redis():
    """Check Redis health using docker exec"""
    try:
        result = subprocess.run([
            'docker', 'exec', 
            subprocess.check_output(['docker-compose', '-f', 'docker-compose.hybrid.yml', 'ps', '-q', 'redis']).decode().strip(),
            'redis-cli', 'ping'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and 'PONG' in result.stdout:
            print(f"{Colors.GREEN}✅ {'Redis':<25} - Healthy{Colors.END}")
            return True
        else:
            print(f"{Colors.RED}❌ {'Redis':<25} - Not responding{Colors.END}")
            return False
    except Exception as e:
        print(f"{Colors.RED}❌ {'Redis':<25} - Error: {str(e)}{Colors.END}")
        return False

def check_docker_services():
    """Check Docker container status"""
    try:
        result = subprocess.run([
            'docker-compose', '-f', 'docker-compose.hybrid.yml', 'ps', '--format', 'json'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            containers = []
            for line in result.stdout.strip().split('\n'):
                if line:
                    containers.append(json.loads(line))
            
            print(f"\n{Colors.BLUE}{Colors.BOLD}📊 Docker Container Status:{Colors.END}")
            print("-" * 70)
            
            all_healthy = True
            for container in containers:
                name = container.get('Name', 'Unknown')
                state = container.get('State', 'Unknown')
                status = container.get('Status', 'Unknown')
                
                if state == 'running':
                    if 'healthy' in status.lower():
                        print(f"{Colors.GREEN}✅ {name:<25} - {status}{Colors.END}")
                    else:
                        print(f"{Colors.YELLOW}⚠️  {name:<25} - {status}{Colors.END}")
                else:
                    print(f"{Colors.RED}❌ {name:<25} - {state}{Colors.END}")
                    all_healthy = False
            
            return all_healthy
        else:
            print(f"{Colors.RED}❌ Failed to get container status{Colors.END}")
            return False
    except Exception as e:
        print(f"{Colors.RED}❌ Error checking containers: {str(e)}{Colors.END}")
        return False

def test_gateway_routing():
    """Test gateway routing functionality"""
    print(f"\n{Colors.PURPLE}{Colors.BOLD}🔀 Testing Gateway Routing:{Colors.END}")
    print("-" * 70)
    
    # Test APISIX routing
    apisix_tests = [
        ("http://localhost:9080/", "APISIX Root"),
        ("http://localhost:9080/health", "APISIX Health"),
        ("http://localhost:9080/auth/health", "APISIX → Auth Service"),
        ("http://localhost:9080/api/v1/auth/health", "APISIX → Auth API"),
    ]
    
    # Test Envoy routing
    envoy_tests = [
        ("http://localhost:10000/", "Envoy Root"),
        ("http://localhost:10000/health", "Envoy Health"),
        ("http://localhost:10000/admin/health", "Envoy → Admin Service"),
        ("http://localhost:10000/api/v1/admin/health", "Envoy → Admin API"),
    ]
    
    all_routes_working = True
    
    for url, name in apisix_tests + envoy_tests:
        success, _ = check_service(url, name, timeout=5)
        if not success:
            all_routes_working = False
    
    return all_routes_working

def get_service_metrics():
    """Get metrics from services"""
    print(f"\n{Colors.CYAN}{Colors.BOLD}📈 Service Metrics:{Colors.END}")
    print("-" * 70)
    
    # APISIX Metrics
    try:
        response = requests.get("http://localhost:9091/apisix/prometheus/metrics", timeout=5)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ APISIX Metrics Available{Colors.END}")
        else:
            print(f"{Colors.YELLOW}⚠️  APISIX Metrics - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ APISIX Metrics - Not available{Colors.END}")
    
    # Envoy Admin
    try:
        response = requests.get("http://localhost:9901/stats", timeout=5)
        if response.status_code == 200:
            print(f"{Colors.GREEN}✅ Envoy Admin Interface Available{Colors.END}")
        else:
            print(f"{Colors.YELLOW}⚠️  Envoy Admin - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ Envoy Admin - Not available{Colors.END}")
    
    # Redis Stats
    try:
        response = requests.get("http://localhost:8002/health/cache/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"{Colors.GREEN}✅ Redis Cache Stats:{Colors.END}")
            print(f"   - Status: {stats.get('status', 'unknown')}")
            print(f"   - Hits: {stats.get('keyspace_hits', 'N/A')}")
            print(f"   - Misses: {stats.get('keyspace_misses', 'N/A')}")
        else:
            print(f"{Colors.YELLOW}⚠️  Redis Stats - HTTP {response.status_code}{Colors.END}")
    except:
        print(f"{Colors.RED}❌ Redis Stats - Not available{Colors.END}")

def main():
    print_header()
    
    # Core Services Health Check
    print(f"{Colors.BLUE}{Colors.BOLD}🏥 Core Services Health:{Colors.END}")
    print("-" * 70)
    
    services = [
        ("http://localhost:9080/health", "APISIX Gateway"),
        ("http://localhost:10000/health", "Envoy Gateway"),
        ("http://localhost:8002/health", "Auth Service"),
        ("http://localhost:8001/health", "Admin Service"),
        ("http://localhost:5050/misc/ping", "pgAdmin"),
    ]
    
    healthy_services = 0
    total_services = len(services) + 1  # +1 for Redis
    
    for url, name in services:
        success, _ = check_service(url, name)
        if success:
            healthy_services += 1
    
    # Check Redis separately
    if check_redis():
        healthy_services += 1
    
    # Check Docker containers
    containers_healthy = check_docker_services()
    
    # Test gateway routing
    routing_working = test_gateway_routing()
    
    # Get service metrics
    get_service_metrics()
    
    # Summary
    print(f"\n{Colors.BOLD}📋 Health Summary:{Colors.END}")
    print("-" * 70)
    print(f"Services Healthy: {healthy_services}/{total_services}")
    print(f"Containers Status: {'✅ All Running' if containers_healthy else '❌ Issues Detected'}")
    print(f"Gateway Routing: {'✅ Working' if routing_working else '❌ Issues Detected'}")
    
    if healthy_services == total_services and containers_healthy and routing_working:
        print(f"\n{Colors.GREEN}{Colors.BOLD}🎉 All systems are healthy and operational!{Colors.END}")
        
        print(f"\n{Colors.WHITE}{Colors.BOLD}🌐 Quick Access URLs:{Colors.END}")
        print(f"   - APISIX Gateway:     http://localhost:9080")
        print(f"   - Envoy Gateway:      http://localhost:10000")
        print(f"   - Auth Service:       http://localhost:8002")
        print(f"   - Admin Service:      http://localhost:8001")
        print(f"   - pgAdmin:            http://localhost:5050")
        
        return 0
    else:
        print(f"\n{Colors.RED}{Colors.BOLD}⚠️  Some issues detected. Check logs:{Colors.END}")
        print(f"   docker-compose -f docker-compose.hybrid.yml logs")
        return 1

if __name__ == "__main__":
    sys.exit(main())
