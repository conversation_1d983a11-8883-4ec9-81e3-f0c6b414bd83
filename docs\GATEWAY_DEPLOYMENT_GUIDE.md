# 🚀 ZCare API Gateway Deployment Guide

This guide provides step-by-step instructions for deploying the ZCare platform with multiple API gateway options.

## 📋 Prerequisites

### System Requirements
- **Docker:** Version 20.10 or higher
- **Docker Compose:** Version 2.0 or higher
- **RAM:** Minimum 8GB (16GB recommended)
- **Storage:** At least 10GB free space
- **OS:** Windows 10/11, macOS 10.15+, or Linux (Ubuntu 18.04+)

### Network Requirements
- Ports 8000-8081, 9090-9092 should be available
- Internet connection for downloading Docker images

## 🔧 Initial Setup

### 1. <PERSON>lone and Navigate to Project
```bash
git clone <repository-url>
cd zionix-be-v1
```

### 2. Set Environment Variables
Create a `.env` file in the root directory:
```bash
# RSA Keys for hybrid encryption (generate using the provided scripts)
RSA_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
RSA_PUBLIC_KEY="-----B<PERSON>IN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"

# Database Configuration
POSTGRES_PASSWORD=Arunnathan
JWT_SECRET_KEY=supersecretkey

# Optional: Kafka Configuration
KAFKA_ENABLED=true
```

### 3. Generate RSA Keys (if not already done)
```bash
# Generate keys
openssl genrsa -out private.pem 2048
openssl rsa -in private.pem -pubout -out public.pem

# Convert to environment variable format
cat private.pem | sed ':a;N;$!ba;s/\n/\\n/g'
cat public.pem | sed ':a;N;$!ba;s/\n/\\n/g'
```

## 🚀 Deployment Options

### Option 1: Quick Start with Gateway Selector

**Windows:**
```cmd
cd gateway
start-gateway.bat
```

**Linux/macOS:**
```bash
cd gateway
chmod +x start-gateway.sh
./start-gateway.sh
```

### Option 2: Manual Gateway Selection

Choose one of the following deployment methods:

#### A. Kong Gateway (Default)
```bash
# Start Kong with all services
docker-compose up -d

# Verify deployment
curl http://localhost:8000/health
```

#### B. NGINX Gateway
```bash
cd gateway/nginx
docker-compose up -d

# Verify deployment
curl http://localhost:8080/health
```

#### C. Traefik Gateway
```bash
cd gateway/traefik
docker-compose up -d

# Verify deployment
curl http://localhost:8080/health
# Access dashboard: http://localhost:8081/dashboard/
```

#### D. Envoy Gateway
```bash
cd gateway/envoy
docker-compose up -d

# Verify deployment
curl http://localhost:8080/health
# Access admin: http://localhost:9901
```

#### E. APISIX Gateway
```bash
cd gateway/apisix
docker-compose up -d

# Verify deployment
curl http://localhost:8080/health
# Access metrics: http://localhost:9091
```

## 🧪 Testing the Deployment

### 1. Automated Testing
```bash
# Test all gateways
python test-gateway.py

# Test specific gateway
python test-gateway.py nginx
```

### 2. Manual Testing

#### Health Checks
```bash
# Gateway health
curl http://localhost:8080/health

# Service health
curl http://localhost:8080/api/v1/auth/health
curl http://localhost:8080/api/v1/admin/health
```

#### API Testing
```bash
# Get public key
curl http://localhost:8080/api/v1/auth/encryption/public-key

# Test CORS
curl -X OPTIONS http://localhost:8080/api/v1/auth/login \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST"
```

## 📊 Monitoring and Management

### Gateway-Specific Dashboards

| Gateway | Dashboard URL | Admin URL |
|---------|---------------|-----------|
| Kong | - | http://localhost:8444 |
| NGINX | - | - |
| Traefik | http://localhost:8081/dashboard/ | - |
| Envoy | - | http://localhost:9901 |
| APISIX | - | http://localhost:9092 |

### Service Monitoring
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs -f <service-name>

# Monitor resource usage
docker stats
```

## 🔧 Configuration Management

### Gateway Configuration Files

| Gateway | Configuration File | Location |
|---------|-------------------|----------|
| Kong | kong.yml | gateway/kong/ |
| NGINX | nginx.conf | gateway/nginx/ |
| Traefik | traefik.yaml | gateway/traefik/ |
| Envoy | envoy.yaml | gateway/envoy/ |
| APISIX | config.yaml, apisix.yaml | gateway/apisix/conf/ |

### Modifying Configurations
1. Edit the configuration file
2. Restart the gateway:
   ```bash
   docker-compose restart <gateway-service>
   ```

## 🚦 Service Endpoints

### Authentication Service (Port varies by gateway)
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/verify-token` - Token verification
- `GET /api/v1/auth/users/me` - Get current user
- `POST /api/v1/auth/users/` - User registration
- `GET /api/v1/auth/encryption/public-key` - Get public key

### Admin Service
- `POST /api/v1/admin/domains/create_domain/` - Create domain
- `GET /api/v1/admin/domains/get_all_domains/` - List domains
- `POST /api/v1/admin/applications/create_application/` - Create application
- `GET /api/v1/admin/encryption/public-key` - Get public key

## 🛑 Stopping Services

### Stop All Gateways
```bash
# Windows
gateway\stop-gateway.bat

# Linux/macOS
./gateway/stop-gateway.sh
```

### Stop Specific Gateway
```bash
cd gateway/<gateway-name>
docker-compose down
```

### Complete Cleanup
```bash
# Stop and remove all containers, networks, and volumes
docker-compose down -v
docker system prune -f
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Conflicts
**Error:** `Port already in use`
**Solution:**
```bash
# Find process using port
netstat -tulpn | grep :8080
# Kill process or change port in docker-compose.yml
```

#### 2. Service Not Starting
**Error:** Container exits immediately
**Solution:**
```bash
# Check logs
docker-compose logs <service-name>
# Common fixes: Check environment variables, file permissions
```

#### 3. Database Connection Issues
**Error:** `Connection refused` to PostgreSQL
**Solution:**
```bash
# Wait for database to be ready
docker-compose logs postgres-auth
docker-compose logs postgres-admin
# Restart services if needed
docker-compose restart auth-service admin-service
```

#### 4. 502 Bad Gateway
**Error:** Gateway returns 502
**Solution:**
```bash
# Check backend service health
curl http://localhost:8001/health  # Admin service direct
curl http://localhost:8002/health  # Auth service direct
# Restart services
docker-compose restart admin-service auth-service
```

### Debug Commands
```bash
# Container status
docker ps -a

# Network inspection
docker network ls
docker network inspect <network-name>

# Volume inspection
docker volume ls
docker volume inspect <volume-name>

# Resource usage
docker stats --no-stream
```

## 🔒 Security Considerations

### Development vs Production

**Development (Current Setup):**
- Permissive CORS (`*`)
- Basic rate limiting
- HTTP only
- Default passwords

**Production Recommendations:**
- Specific CORS origins
- Stricter rate limiting
- HTTPS with SSL certificates
- Strong passwords and secrets
- Network segmentation
- Regular security updates

### Securing the Deployment
1. Change default passwords
2. Use environment-specific configurations
3. Enable HTTPS/TLS
4. Implement proper authentication
5. Set up monitoring and alerting
6. Regular backups

## 📈 Performance Optimization

### Resource Allocation
```yaml
# Add to docker-compose.yml services
deploy:
  resources:
    limits:
      cpus: '0.5'
      memory: 512M
    reservations:
      cpus: '0.25'
      memory: 256M
```

### Scaling Services
```bash
# Scale specific service
docker-compose up -d --scale auth-service=3

# Load balancing is handled by the gateway
```

## 📝 Next Steps

1. **Choose your preferred gateway** based on requirements
2. **Set up monitoring** with Prometheus/Grafana
3. **Configure SSL/TLS** for production
4. **Implement CI/CD** pipelines
5. **Set up backup strategies**
6. **Plan for scaling** and high availability

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Docker logs
3. Consult gateway-specific documentation
4. Create an issue in the project repository
