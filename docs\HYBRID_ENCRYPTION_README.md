# ZCare Platform - Hybrid Encryption Implementation

## Overview

The ZCare Platform now includes comprehensive hybrid encryption functionality across both the Admin Service and Auth Service. This implementation combines RSA (asymmetric) and AES (symmetric) encryption to provide both security and performance.

## 🔐 Hybrid Encryption Architecture

### How It Works

1. **RSA (2048-bit)**: Used for key exchange and encrypting the AES key
2. **AES (256-bit CBC)**: Used for encrypting the actual data (faster for large data)
3. **Initialization Vector (IV)**: Random 128-bit IV for each encryption operation
4. **Base64 Encoding**: All encrypted data is base64 encoded for safe storage/transmission

### Benefits

- **Security**: RSA provides strong key exchange security
- **Performance**: AES provides fast bulk data encryption
- **Scalability**: Can handle large amounts of data efficiently
- **Compatibility**: Standard algorithms supported across platforms

## 🚀 Quick Start

### Using Docker (Recommended)

1. **Start all services:**
   ```bash
   # Linux/Mac
   ./start-services.sh
   
   # Windows
   start-services.bat
   ```

2. **Access the services:**
   - Admin Service: http://localhost:8001/docs
   - Auth Service: http://localhost:8002/docs

### Manual Setup

1. **Install dependencies:**
   ```bash
   # Admin Service
   cd admin-service
   pip install -r requirements.txt
   
   # Auth Service
   cd auth-service
   pip install -r requirements.txt
   ```

2. **Set environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your RSA keys
   ```

3. **Run services:**
   ```bash
   # Terminal 1 - Auth Service
   cd auth-service
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   
   # Terminal 2 - Admin Service
   cd admin-service
   uvicorn app.main:app --host 0.0.0.0 --port 8001
   ```

## 🔧 Configuration

### RSA Key Configuration

The services can use RSA keys from environment variables or generate them automatically:

```bash
# Option 1: Use environment variables
export RSA_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----"
export RSA_PUBLIC_KEY="-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----"

# Option 2: Let the service generate keys automatically (for development)
# Keys will be generated on first startup if not provided
```

### Generating Your Own RSA Keys

```bash
# Generate private key
openssl genrsa -out private.pem 2048

# Generate public key
openssl rsa -in private.pem -pubout -out public.pem

# Convert to environment variable format
cat private.pem | sed ':a;N;$!ba;s/\n/\\n/g'
cat public.pem | sed ':a;N;$!ba;s/\n/\\n/g'
```

## 📡 API Endpoints

### Admin Service Encryption APIs

Base URL: `http://localhost:8001/api/v1/encryption`

- `GET /public-key` - Get server's public key
- `POST /generate-keypair` - Generate new RSA key pair
- `POST /encrypt` - Encrypt data
- `POST /decrypt` - Decrypt data
- `POST /encrypt-bulk` - Encrypt multiple items
- `POST /decrypt-bulk` - Decrypt multiple items

### Auth Service Encryption APIs

Base URL: `http://localhost:8002/api/v1/encryption`

- Same endpoints as Admin Service
- Additional user authentication required

## 🔒 Automatic Data Encryption

### Admin Service

The following fields are automatically encrypted when stored in the database:

- **Domain.action**: Sensitive domain configuration data
- **Application.config**: Application configuration data

### Auth Service

The following fields are automatically encrypted when stored in the database:

- **User.phone**: User phone numbers
- **User.address**: User addresses  
- **User.notes**: User notes and sensitive information

## 💻 Usage Examples

### Basic Encryption/Decryption

```python
from app.core.hybrid_encryption import hybrid_encryption

# Encrypt data
data = "Sensitive information"
encrypted_package = hybrid_encryption.encrypt_data(data)

# Decrypt data
decrypted_data = hybrid_encryption.decrypt_data(encrypted_package)
```

### Using Custom Keys

```python
# Generate key pair
private_key_pem, public_key_pem = hybrid_encryption.generate_key_pair()

# Encrypt with custom public key
encrypted_package = hybrid_encryption.encrypt_data(data, public_key_pem)

# Decrypt with custom private key
decrypted_data = hybrid_encryption.decrypt_data(encrypted_package, private_key_pem)
```

### API Usage

```bash
# Get public key
curl -X GET "http://localhost:8001/api/v1/encryption/public-key"

# Encrypt data
curl -X POST "http://localhost:8001/api/v1/encryption/encrypt" \
  -H "Content-Type: application/json" \
  -d '{"data": "Hello, World!"}'

# Decrypt data
curl -X POST "http://localhost:8001/api/v1/encryption/decrypt" \
  -H "Content-Type: application/json" \
  -d '{
    "encrypted_data": "...",
    "encrypted_key": "...",
    "iv": "...",
    "algorithm": "AES-256-CBC",
    "key_algorithm": "RSA-OAEP"
  }'
```

## 🧪 Testing

### Run Encryption Tests

```bash
python test_encryption.py
```

This will test:
- Import validation
- Admin Service encryption/decryption
- Auth Service encryption/decryption
- Key generation
- Sensitive field encryption

### Manual Testing

1. **Access API documentation:**
   - Admin Service: http://localhost:8001/docs
   - Auth Service: http://localhost:8002/docs

2. **Test encryption endpoints using the interactive docs**

3. **Create test data and verify encryption in database**

## 🔍 Monitoring and Logs

### Check Service Status

```bash
docker-compose ps
```

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f admin-service
docker-compose logs -f auth-service
```

### Health Checks

- Admin Service: http://localhost:8001/health
- Auth Service: http://localhost:8002/health

## 🛠️ Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed
2. **Key Generation Errors**: Check RSA key format in environment variables
3. **Database Connection**: Ensure PostgreSQL is running and accessible
4. **Port Conflicts**: Check if ports 8001, 8002 are available

### Debug Mode

Set environment variable for detailed logging:
```bash
export LOG_LEVEL=DEBUG
```

## 🔐 Security Considerations

### Production Deployment

1. **Use strong RSA keys** (2048-bit minimum, 4096-bit recommended)
2. **Store keys securely** (use secrets management systems)
3. **Rotate keys regularly**
4. **Use HTTPS** for all API communications
5. **Implement proper authentication** and authorization
6. **Monitor encryption/decryption operations**

### Key Management

- Never store private keys in code or configuration files
- Use environment variables or secure key management systems
- Implement key rotation procedures
- Backup keys securely

## 📚 Additional Resources

- [Cryptography Documentation](https://cryptography.io/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
