# Redis Cache and pgAdmin Integration

This document describes the Redis caching implementation and pgAdmin database management integration for the ZCare platform.

## Overview

The auth-service now includes Redis caching for improved performance and pgAdmin for database management. This integration provides:

- **Redis Caching**: Fast in-memory caching for user data, authentication tokens, and frequently accessed data
- **pgAdmin**: Web-based PostgreSQL administration tool for database management
- **Health Monitoring**: Comprehensive health checks and monitoring for Redis connectivity
- **Docker Integration**: All services are containerized and orchestrated via Docker Compose

## Features

### Redis Caching
- User data caching (by ID and email)
- Authentication token caching
- Login token storage
- Configurable TTL (Time To Live)
- Automatic cache invalidation on data updates
- Health checks and monitoring endpoints

### pgAdmin Integration
- Web-based database administration
- Support for both auth-service and admin-service databases
- Pre-configured connections
- Accessible at http://localhost:5050

## Configuration

### Environment Variables

The following environment variables control Redis and caching behavior:

```bash
# Redis Configuration
REDIS_HOST=redis                    # Redis server hostname
REDIS_PORT=6379                     # Redis server port
REDIS_DB=0                          # Redis database number
REDIS_PASSWORD=                     # Redis password (optional)
REDIS_URL=redis://redis:6379/0      # Complete Redis URL

# Cache Configuration
ENABLE_CACHE=true                   # Enable/disable caching
CACHE_TTL=300                       # Cache TTL in seconds (5 minutes)
```

### pgAdmin Configuration

```bash
# pgAdmin Configuration
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin123
PGADMIN_CONFIG_SERVER_MODE=False
```

## Docker Services

### Redis Service
```yaml
redis:
  image: redis:7-alpine
  ports:
    - "6379:6379"
  volumes:
    - redis-data:/data
  command: redis-server --appendonly yes
  healthcheck:
    test: ["CMD", "redis-cli", "ping"]
    interval: 10s
    timeout: 5s
    retries: 5
    start_period: 30s
  restart: unless-stopped
```

### pgAdmin Service
```yaml
pgadmin:
  image: dpage/pgadmin4:latest
  environment:
    - PGADMIN_DEFAULT_EMAIL=<EMAIL>
    - PGADMIN_DEFAULT_PASSWORD=admin123
    - PGADMIN_CONFIG_SERVER_MODE=False
  ports:
    - "5050:80"
  volumes:
    - pgadmin-data:/var/lib/pgadmin
  depends_on:
    - postgres-admin
    - postgres-auth
  restart: unless-stopped
```

## API Endpoints

### Health Check Endpoints

#### General Health Check
```
GET /health
```
Returns overall service health including Redis status.

#### Redis Health Check
```
GET /health/redis
```
Returns detailed Redis health information.

#### Cache Statistics
```
GET /health/cache/stats
```
Returns Redis cache statistics and performance metrics.

## Caching Strategy

### User Data Caching
- **Cache Keys**: 
  - `user:{user_id}` - User data by ID
  - `user:email:{email}` - User data by email
  - `login_token:{user_id}` - User login tokens

### Cache Operations
- **GET**: Retrieve cached data with fallback to database
- **SET**: Store data with configurable TTL
- **DELETE**: Remove specific cache entries
- **INVALIDATE**: Clear cache on data updates

### Cache Invalidation
- Automatic invalidation on user updates
- Cache clearing on user deletion
- Pattern-based cache clearing for bulk operations

## Usage

### Starting Services
```bash
# Start all services including Redis and pgAdmin
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs redis
docker-compose logs pgadmin
```

### Accessing pgAdmin
1. Open http://localhost:5050 in your browser
2. Login with:
   - Email: <EMAIL>
   - Password: admin123
3. Add database servers:
   - **Auth Database**: postgres-auth:5432
   - **Admin Database**: postgres-admin:5432

### Testing Redis Integration
```bash
# Run the integration test script
python test_redis_integration.py

# Manual Redis testing
docker exec -it <redis_container> redis-cli
> ping
> keys *
> get user:1
```

## Monitoring

### Redis Metrics
- Connected clients
- Memory usage
- Cache hit/miss ratio
- Commands processed
- Uptime

### Health Checks
- Redis connectivity
- Cache performance
- Service dependencies

## Performance Benefits

### Expected Improvements
- **User Lookups**: 50-80% faster response times
- **Authentication**: Reduced database load
- **API Response Times**: Improved overall performance
- **Database Load**: Reduced query frequency

### Cache Hit Rates
- Target: >80% cache hit rate for user lookups
- Monitoring: Available via `/health/cache/stats` endpoint

## Troubleshooting

### Common Issues

#### Redis Connection Failed
```bash
# Check Redis container status
docker-compose logs redis

# Verify Redis is running
docker exec -it <redis_container> redis-cli ping
```

#### Cache Not Working
```bash
# Check cache configuration
curl http://localhost:8002/health/redis

# Verify environment variables
docker-compose exec auth-service env | grep REDIS
```

#### pgAdmin Access Issues
```bash
# Check pgAdmin logs
docker-compose logs pgadmin

# Verify port mapping
docker-compose ps pgadmin
```

### Performance Issues
- Monitor cache hit rates
- Check Redis memory usage
- Verify TTL settings
- Review cache invalidation patterns

## Security Considerations

### Redis Security
- Redis runs in isolated Docker network
- No external Redis access by default
- Optional password protection available

### pgAdmin Security
- Default credentials should be changed in production
- Access restricted to development environment
- Database connections use internal Docker networking

## Development

### Adding New Cache Operations
1. Define cache keys in `app/core/cache.py`
2. Implement caching in CRUD operations
3. Add cache invalidation logic
4. Update health checks if needed

### Testing Cache Implementation
1. Use the provided test script
2. Monitor cache statistics
3. Verify cache invalidation
4. Test performance improvements

## Production Considerations

### Redis Configuration
- Enable Redis persistence
- Configure memory limits
- Set up Redis clustering for high availability
- Implement Redis monitoring

### pgAdmin Configuration
- Change default credentials
- Restrict network access
- Use HTTPS in production
- Configure backup strategies

### Monitoring
- Set up Redis monitoring alerts
- Monitor cache performance metrics
- Track database load reduction
- Monitor service dependencies
