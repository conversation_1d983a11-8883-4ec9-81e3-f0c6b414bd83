# 🚀 Running ZCare Applications with Redis & pgAdmin

## Quick Start

### 1. Start All Services
```bash
docker-compose up -d --build
```

### 2. Wait for Services (30-60 seconds)
```bash
# Check status
docker-compose ps

# Check logs if needed
docker-compose logs -f
```

### 3. Verify Services are Running
```bash
# Check all containers
docker ps

# Test health endpoints
curl http://localhost:8002/health
curl http://localhost:8001/health
```

## 🌐 Service Access Points

### Main Applications
- **🔐 Auth Service**: http://localhost:8002
- **👥 Admin Service**: http://localhost:8001
- **🗄️ pgAdmin**: http://localhost:5050

### API Documentation
- **📚 Auth Service Swagger**: http://localhost:8002/docs
- **📚 Admin Service Swagger**: http://localhost:8001/docs

### Health & Monitoring
- **❤️ Auth Health**: http://localhost:8002/health
- **🔴 Redis Health**: http://localhost:8002/health/redis
- **📊 Cache Stats**: http://localhost:8002/health/cache/stats
- **❤️ Admin Health**: http://localhost:8001/health

## 🔑 Login Credentials

### pgAdmin Access
- **URL**: http://localhost:5050
- **Email**: <EMAIL>
- **Password**: admin123

### Database Connections (for pgAdmin)
- **Auth Database**: 
  - Host: postgres-auth
  - Port: 5432
  - Database: auth_service
  - Username: postgres
  - Password: Arunnathan

- **Admin Database**:
  - Host: postgres-admin
  - Port: 5432
  - Database: admin_service
  - Username: postgres
  - Password: Arunnathan

## 🧪 Testing Redis Caching

### Run Test Scripts
```bash
# Simple Redis test
python simple_redis_test.py

# Comprehensive test
python test_registration_redis.py
```

### Manual Testing Steps

#### 1. Register a New User
```bash
curl -X POST http://localhost:8002/api/v1/users/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User",
    "password": "testpassword123",
    "confirm_password": "testpassword123",
    "status": true
  }'
```

#### 2. Login to Get Token
```bash
curl -X POST http://localhost:8002/api/v1/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=testpassword123"
```

#### 3. Test User Lookup (Should Hit Cache)
```bash
curl -X GET http://localhost:8002/api/v1/users/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

#### 4. Check Redis Cache Directly
```bash
# Connect to Redis container
docker exec -it <redis_container_name> redis-cli

# Check cached keys
keys user:*

# View cached user data
get user:1
get user:email:<EMAIL>
```

## 📊 Service Status Verification

### Check All Services
```bash
docker-compose ps
```

Expected output:
```
NAME                    STATUS              PORTS
admin-service           Up                  0.0.0.0:8001->8000/tcp
auth-service            Up                  0.0.0.0:8002->8000/tcp
pgadmin                 Up                  0.0.0.0:5050->80/tcp
postgres-admin          Up (healthy)        0.0.0.0:5434->5432/tcp
postgres-auth           Up (healthy)        0.0.0.0:5433->5432/tcp
redis                   Up (healthy)        0.0.0.0:6379->6379/tcp
```

### Check Service Logs
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs auth-service
docker-compose logs redis
docker-compose logs pgadmin
```

## 🔧 Troubleshooting

### Services Not Starting
```bash
# Stop all services
docker-compose down

# Remove volumes (if needed)
docker-compose down -v

# Rebuild and start
docker-compose up -d --build
```

### Redis Connection Issues
```bash
# Check Redis container
docker-compose logs redis

# Test Redis directly
docker exec -it <redis_container> redis-cli ping
```

### Database Connection Issues
```bash
# Check database containers
docker-compose logs postgres-auth
docker-compose logs postgres-admin

# Test database connection
docker exec -it <postgres_container> psql -U postgres -d auth_service -c "SELECT 1;"
```

### Port Conflicts
If ports are already in use, modify `docker-compose.yml`:
```yaml
ports:
  - "8003:8000"  # Change 8002 to 8003 for auth-service
  - "8004:8000"  # Change 8001 to 8004 for admin-service
  - "5051:80"    # Change 5050 to 5051 for pgAdmin
```

## 🎯 Performance Testing

### Test Cache Performance
```bash
# First request (database hit)
time curl -H "Authorization: Bearer TOKEN" http://localhost:8002/api/v1/users/me

# Second request (cache hit - should be faster)
time curl -H "Authorization: Bearer TOKEN" http://localhost:8002/api/v1/users/me
```

### Monitor Cache Statistics
```bash
# Get cache stats
curl http://localhost:8002/health/cache/stats

# Expected response:
{
  "status": "connected",
  "redis_version": "7.x.x",
  "connected_clients": 1,
  "used_memory": "1.2M",
  "keyspace_hits": 150,
  "keyspace_misses": 25
}
```

## 🛑 Stopping Services

### Stop All Services
```bash
docker-compose down
```

### Stop and Remove Volumes
```bash
docker-compose down -v
```

### Stop Specific Service
```bash
docker-compose stop auth-service
docker-compose stop redis
```

## 📋 Service Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Auth Service  │    │  Admin Service  │    │     pgAdmin     │
│   Port: 8002    │    │   Port: 8001    │    │   Port: 5050    │
└─────────┬───────┘    └─────────┬───────┘    └─────────────────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│     Redis       │    │   PostgreSQL    │
│   Port: 6379    │    │ Auth: 5433      │
│   (Cache)       │    │ Admin: 5434     │
└─────────────────┘    └─────────────────┘
```

## ✅ Success Indicators

### All Services Running
- ✅ All containers show "Up" status
- ✅ Health endpoints return 200 OK
- ✅ Redis shows "healthy" status
- ✅ PostgreSQL shows "healthy" status

### Redis Caching Working
- ✅ User registration creates cache entries
- ✅ User lookups hit cache (faster response times)
- ✅ Cache statistics show hits > misses
- ✅ Redis health endpoint returns "connected"

### pgAdmin Accessible
- ✅ pgAdmin loads at http://localhost:5050
- ✅ Can login with provided credentials
- ✅ Can connect to both databases

## 🎉 You're Ready!

Once all services are running and tests pass, you have:
- ✅ Auth service with Redis caching
- ✅ Admin service with database
- ✅ pgAdmin for database management
- ✅ Comprehensive health monitoring
- ✅ Performance improvements through caching

**Happy coding! 🚀**
