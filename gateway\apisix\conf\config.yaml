apisix:
  node_listen: 9080
  enable_ipv6: false
  enable_control: true
  control:
    ip: "0.0.0.0"
    port: 9092

deployment:
  role: traditional
  role_traditional:
    config_provider: yaml

etcd:
  use_grpc: false
  host:
    - "http://127.0.0.1:2379"
  prefix: "/apisix"
  timeout: 30

nginx_config:
  error_log: "/usr/local/apisix/logs/error.log"
  error_log_level: "warn"
  worker_rlimit_nofile: 20480
  event:
    worker_connections: 10620
  http:
    access_log: "/usr/local/apisix/logs/access.log"
    keepalive_timeout: 60s
    client_header_timeout: 60s
    client_body_timeout: 60s
    send_timeout: 10s
    underscores_in_headers: "on"
    real_ip_header: "X-Real-IP"
    real_ip_from:
      - 127.0.0.1
      - 'unix:'

plugin_attr:
  prometheus:
    export_addr:
      ip: "0.0.0.0"
      port: 9091

plugins:
  - real-ip
  - client-control
  - proxy-control
  - request-id
  - zipkin
  - ext-plugin-pre-req
  - fault-injection
  - mocking
  - serverless-pre-function
  - cors
  - ip-restriction
  - ua-restriction
  - referer-restriction
  - csrf
  - uri-blocker
  - request-validation
  - openid-connect
  - authz-casbin
  - authz-casdoor
  - wolf-rbac
  - ldap-auth
  - hmac-auth
  - basic-auth
  - jwt-auth
  - key-auth
  - consumer-restriction
  - authz-keycloak
  - opa
  - forward-auth
  - multi-auth
  - api-breaker
  - limit-req
  - limit-count
  - limit-conn
  - degraphql
  - body-transformer
  - workflow
  - proxy-cache
  - proxy-mirror
  - proxy-rewrite
  - workflow
  - api-breaker
  - limit-req
  - limit-count
  - limit-conn
  - gzip
  - server-info
  - traffic-split
  - redirect
  - response-rewrite
  - kafka-logger
  - rocketmq-logger
  - tcp-logger
  - kafka-proxy
  - dubbo-proxy
  - grpc-transcode
  - grpc-web
  - public-api
  - prometheus
  - datadog
  - echo
  - http-logger
  - splunk-hec-logging
  - skywalking-logger
  - google-cloud-logging
  - sls-logger
  - tcp-logger
  - kafka-logger
  - rocketmq-logger
  - udp-logger
  - clickhouse-logger
  - tencent-cloud-cls
  - inspect
  - example-plugin
  - aws-lambda
  - azure-functions
  - openwhisk
  - serverless-post-function
  - ext-plugin-post-req
  - ext-plugin-post-resp
