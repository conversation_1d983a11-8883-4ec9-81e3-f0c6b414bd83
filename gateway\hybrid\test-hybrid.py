#!/usr/bin/env python3
"""
ZCare Hybrid Gateway Test Script
Tests APISIX (auth) and Envoy (admin) gateways
"""

import requests
import json
import time
import sys

# Gateway configurations
GATEWAYS = {
    "apisix-auth": {
        "url": "http://localhost:8080", 
        "name": "APISIX Auth Gateway",
        "service": "auth-service"
    },
    "envoy-admin": {
        "url": "http://localhost:8081", 
        "name": "Envoy Admin Gateway",
        "service": "admin-service"
    }
}

# Test endpoints for each gateway
AUTH_ENDPOINTS = [
    {"path": "/health", "method": "GET", "expected_status": 200},
    {"path": "/", "method": "GET", "expected_status": 200},
    {"path": "/api/v1/auth/encryption/public-key", "method": "GET", "expected_status": 200},
]

ADMIN_ENDPOINTS = [
    {"path": "/health", "method": "GET", "expected_status": 200},
    {"path": "/", "method": "GET", "expected_status": 200},
    {"path": "/api/v1/admin/encryption/public-key", "method": "GET", "expected_status": 200},
]

def test_endpoint(base_url: str, endpoint: dict) -> dict:
    """Test a single endpoint"""
    url = f"{base_url}{endpoint['path']}"
    method = endpoint['method']
    expected_status = endpoint['expected_status']
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            response = requests.post(url, timeout=10)
        else:
            return {"success": False, "error": f"Unsupported method: {method}"}
        
        success = response.status_code == expected_status
        return {
            "success": success,
            "status_code": response.status_code,
            "expected_status": expected_status,
            "response_time": response.elapsed.total_seconds(),
            "content_length": len(response.content) if response.content else 0,
            "response_text": response.text[:200] if response.text else ""
        }
    except requests.exceptions.RequestException as e:
        return {"success": False, "error": str(e)}

def test_gateway(gateway_name: str, gateway_config: dict, endpoints: list) -> dict:
    """Test all endpoints for a specific gateway"""
    print(f"\n🧪 Testing {gateway_config['name']} ({gateway_config['url']})")
    print("=" * 70)
    
    results = []
    total_tests = len(endpoints)
    passed_tests = 0
    
    for endpoint in endpoints:
        print(f"Testing {endpoint['method']} {endpoint['path']}... ", end="")
        result = test_endpoint(gateway_config['url'], endpoint)
        
        if result['success']:
            print(f"✅ PASS ({result['status_code']}) - {result['response_time']:.3f}s")
            passed_tests += 1
        else:
            if 'error' in result:
                print(f"❌ FAIL - {result['error']}")
            else:
                print(f"❌ FAIL - Expected {result['expected_status']}, got {result['status_code']}")
        
        results.append({
            "endpoint": endpoint['path'],
            "method": endpoint['method'],
            **result
        })
    
    success_rate = (passed_tests / total_tests) * 100
    print(f"\n📊 Results: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
    
    return {
        "gateway": gateway_name,
        "name": gateway_config['name'],
        "url": gateway_config['url'],
        "service": gateway_config['service'],
        "total_tests": total_tests,
        "passed_tests": passed_tests,
        "success_rate": success_rate,
        "results": results
    }

def test_cross_gateway_communication():
    """Test that auth and admin services can communicate through their respective gateways"""
    print(f"\n🔗 Testing Cross-Gateway Communication")
    print("=" * 70)
    
    # This would test scenarios where admin service needs to validate tokens with auth service
    # For now, we'll just verify both gateways are responding
    
    auth_health = test_endpoint("http://localhost:8080", {"path": "/health", "method": "GET", "expected_status": 200})
    admin_health = test_endpoint("http://localhost:8081", {"path": "/health", "method": "GET", "expected_status": 200})
    
    if auth_health['success'] and admin_health['success']:
        print("✅ Both gateways are healthy and can communicate")
        return True
    else:
        print("❌ Gateway communication issues detected")
        return False

def main():
    """Main test function"""
    print("🚀 ZCare Hybrid Gateway Test Suite")
    print("=" * 70)
    print("Testing APISIX (Auth) + Envoy (Admin) Configuration")
    print("=" * 70)
    
    all_results = []
    
    # Test APISIX Auth Gateway
    auth_result = test_gateway("apisix-auth", GATEWAYS["apisix-auth"], AUTH_ENDPOINTS)
    all_results.append(auth_result)
    time.sleep(1)
    
    # Test Envoy Admin Gateway  
    admin_result = test_gateway("envoy-admin", GATEWAYS["envoy-admin"], ADMIN_ENDPOINTS)
    all_results.append(admin_result)
    time.sleep(1)
    
    # Test cross-gateway communication
    cross_comm_success = test_cross_gateway_communication()
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 HYBRID GATEWAY TEST SUMMARY")
    print("=" * 70)
    
    for result in all_results:
        status = "🟢" if result['success_rate'] == 100 else "🟡" if result['success_rate'] >= 50 else "🔴"
        print(f"{status} {result['name']}: {result['passed_tests']}/{result['total_tests']} ({result['success_rate']:.1f}%)")
        print(f"   Service: {result['service']} | URL: {result['url']}")
    
    # Cross-communication status
    comm_status = "🟢" if cross_comm_success else "🔴"
    print(f"{comm_status} Cross-Gateway Communication: {'PASS' if cross_comm_success else 'FAIL'}")
    
    # Overall statistics
    total_tests = sum(r['total_tests'] for r in all_results)
    total_passed = sum(r['passed_tests'] for r in all_results)
    overall_success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n🎯 Overall: {total_passed}/{total_tests} tests passed ({overall_success_rate:.1f}%)")
    
    if overall_success_rate == 100 and cross_comm_success:
        print("🎉 All tests passed! Hybrid gateway setup is working correctly.")
        print("✅ APISIX handling auth-service routes on port 8080")
        print("✅ Envoy handling admin-service routes on port 8081")
    elif overall_success_rate >= 75:
        print("⚠️  Most tests passed. Some components may need attention.")
    else:
        print("❌ Many tests failed. Please check gateway configurations.")
    
    print("\n" + "=" * 70)
    print("🔗 Quick Access URLs:")
    print("   Auth Service (APISIX):  http://localhost:8080")
    print("   Admin Service (Envoy):  http://localhost:8081")
    print("   APISIX Metrics:         http://localhost:9091")
    print("   Envoy Admin:            http://localhost:9901")

if __name__ == "__main__":
    main()
