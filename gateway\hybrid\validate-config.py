#!/usr/bin/env python3
"""
Validate Hybrid Gateway Configuration
Checks if all configuration files are properly set up
"""

import os
import sys

def check_file_exists(filepath, description):
    """Check if a file exists and is readable"""
    if os.path.exists(filepath):
        print(f"✅ {description}: {filepath}")
        return True
    else:
        print(f"❌ {description}: {filepath} - NOT FOUND")
        return False

def validate_yaml_basic(filepath):
    """Basic YAML validation"""
    try:
        with open(filepath, 'r') as f:
            content = f.read()
        
        # Check for basic YAML structure
        if ':' in content and content.strip():
            return True
        return False
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return False

def main():
    """Main validation function"""
    print("🔧 ZCare Hybrid Gateway Configuration Validator")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists('docker-compose.yml'):
        print("❌ Please run this script from the gateway/hybrid directory")
        return False
    
    all_valid = True
    
    # Required files
    files_to_check = [
        ('docker-compose.yml', 'Docker Compose Configuration'),
        ('apisix-config.yaml', 'APISIX Configuration'),
        ('apisix-auth.yaml', 'APISIX Auth Routes'),
        ('envoy-admin.yaml', 'Envoy Admin Configuration'),
        ('start-hybrid.bat', 'Windows Start Script'),
        ('start-hybrid.sh', 'Linux Start Script'),
        ('test-hybrid.py', 'Test Script'),
        ('README.md', 'Documentation')
    ]
    
    print("\n📁 Checking Required Files:")
    print("-" * 40)
    
    for filename, description in files_to_check:
        if not check_file_exists(filename, description):
            all_valid = False
    
    print("\n📋 Validating Configuration Content:")
    print("-" * 40)
    
    # Validate YAML files
    yaml_files = ['docker-compose.yml', 'apisix-config.yaml', 'apisix-auth.yaml', 'envoy-admin.yaml']
    
    for yaml_file in yaml_files:
        if os.path.exists(yaml_file):
            if validate_yaml_basic(yaml_file):
                print(f"✅ {yaml_file}: Valid YAML structure")
            else:
                print(f"❌ {yaml_file}: Invalid YAML structure")
                all_valid = False
    
    print("\n🔍 Configuration Summary:")
    print("-" * 40)
    
    if all_valid:
        print("🎉 All configuration files are present and valid!")
        print("\n🚀 Ready to deploy hybrid gateway:")
        print("   - APISIX for auth-service (Port 8080)")
        print("   - Envoy for admin-service (Port 8081)")
        print("\n📋 Next steps:")
        print("   1. Run: docker-compose up -d")
        print("   2. Test: python test-hybrid.py")
        print("   3. Access: http://localhost:8080 (auth) & http://localhost:8081 (admin)")
        return True
    else:
        print("❌ Some configuration files are missing or invalid")
        print("Please check the errors above and fix them before deployment")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
