from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives import serialization
import os

# Generate new keys with proper Windows file handling
private_key = rsa.generate_private_key(
    public_exponent=65537,
    key_size=2048,
)

# Ensure directory exists and write private key
with open('private.pem', 'wb') as f:
    f.write(private_key.private_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PrivateFormat.PKCS8,
        encryption_algorithm=serialization.NoEncryption()
    ))

# Generate and write public key 
public_key = private_key.public_key()
with open('public.pem', 'wb') as f:
    f.write(public_key.public_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.PublicFormat.SubjectPublicKeyInfo
    ))

print(f"Keys generated at {os.path.abspath('private.pem')} and {os.path.abspath('public.pem')}")
