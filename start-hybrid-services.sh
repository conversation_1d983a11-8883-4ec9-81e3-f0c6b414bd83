#!/bin/bash

# ZCare Hybrid Gateway Services Startup Script
# APISIX for Auth Service + Envoy for Admin Service

set -e

echo "🚀 Starting ZCare Hybrid Gateway Services..."
echo "📋 Architecture:"
echo "   - APISIX Gateway (port 9080) → Auth Service"
echo "   - Envoy Gateway (port 10000) → Admin Service"
echo "   - <PERSON><PERSON> (port 6379)"
echo "   - PostgreSQL Databases (ports 5433, 5434)"
echo "   - pgAdmin (port 5050)"
echo "   - Kafka + Zookeeper (ports 9092, 2181)"
echo "   - Prometheus (port 9090) + <PERSON><PERSON> (port 3000)"
echo ""

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install docker-compose."
    exit 1
fi

# Stop any existing services
echo "🛑 Stopping existing services..."
docker-compose -f docker-compose.hybrid.yml down --remove-orphans

# Clean up any orphaned containers
echo "🧹 Cleaning up..."
docker system prune -f --volumes

# Start services
echo "🔄 Starting hybrid gateway services..."
docker-compose -f docker-compose.hybrid.yml up -d --build

# Wait for services to start
echo "⏳ Waiting for services to initialize (60 seconds)..."
sleep 60

# Check service health
echo "🔍 Checking service health..."

services=(
    "http://localhost:9080/health:APISIX Gateway"
    "http://localhost:10000/health:Envoy Gateway"
    "http://localhost:8002/health:Auth Service"
    "http://localhost:8001/health:Admin Service"
    "http://localhost:6379:Redis"
)

all_healthy=true

for service in "${services[@]}"; do
    url=$(echo $service | cut -d: -f1-2)
    name=$(echo $service | cut -d: -f3)
    
    if [[ $name == "Redis" ]]; then
        if docker exec $(docker-compose -f docker-compose.hybrid.yml ps -q redis) redis-cli ping > /dev/null 2>&1; then
            echo "✅ $name is healthy"
        else
            echo "❌ $name is not responding"
            all_healthy=false
        fi
    else
        if curl -f -s $url > /dev/null 2>&1; then
            echo "✅ $name is healthy"
        else
            echo "❌ $name is not responding"
            all_healthy=false
        fi
    fi
done

echo ""
echo "📊 Service Status:"
docker-compose -f docker-compose.hybrid.yml ps

if $all_healthy; then
    echo ""
    echo "🎉 All services are running successfully!"
    echo ""
    echo "🌐 Access Points:"
    echo "   - APISIX Gateway (Auth):    http://localhost:9080"
    echo "   - Envoy Gateway (Admin):    http://localhost:10000"
    echo "   - Auth Service Direct:      http://localhost:8002"
    echo "   - Admin Service Direct:     http://localhost:8001"
    echo "   - pgAdmin:                  http://localhost:5050"
    echo "   - Prometheus:               http://localhost:9090"
    echo "   - Grafana:                  http://localhost:3000"
    echo ""
    echo "📚 API Documentation:"
    echo "   - Auth Service Swagger:     http://localhost:8002/docs"
    echo "   - Admin Service Swagger:    http://localhost:8001/docs"
    echo ""
    echo "🔧 Gateway Admin Interfaces:"
    echo "   - APISIX Control API:       http://localhost:9092"
    echo "   - Envoy Admin Interface:    http://localhost:9901"
    echo ""
    echo "🧪 Test Commands:"
    echo "   # Test APISIX (Auth Service)"
    echo "   curl http://localhost:9080/auth/health"
    echo "   curl http://localhost:9080/api/v1/auth/health"
    echo ""
    echo "   # Test Envoy (Admin Service)"
    echo "   curl http://localhost:10000/admin/health"
    echo "   curl http://localhost:10000/api/v1/admin/health"
    echo ""
    echo "📋 Credentials:"
    echo "   - pgAdmin: <EMAIL> / admin123"
    echo "   - Database: postgres / Arunnathan"
else
    echo ""
    echo "⚠️  Some services are not healthy. Check logs:"
    echo "   docker-compose -f docker-compose.hybrid.yml logs"
    exit 1
fi
