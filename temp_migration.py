"""update users table to use status and action columns

Revision ID: 2933a3d8ec97
Revises: 005
Create Date: 2025-07-14 08:22:07.131684

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2933a3d8ec97'
down_revision = '005'
branch_labels = None
depends_on = None


def upgrade():
    # Rename is_active to status
    op.alter_column('users', 'is_active', new_column_name='status')

    # Drop is_admin column and add action column
    op.drop_column('users', 'is_admin')
    op.add_column('users', sa.Column('action', sa.String(length=50), nullable=True))


def downgrade():
    # Reverse the changes
    op.alter_column('users', 'status', new_column_name='is_active')
    op.drop_column('users', 'action')
    op.add_column('users', sa.Column('is_admin', sa.<PERSON>(), nullable=True))