apiVersion: apps/v1
kind: Deployment
metadata:
  name: SERVICE_NAME
  labels:
    app: SERVICE_NAME
    service: backend
    tier: microservice
spec:
  replicas: 2
  selector:
    matchLabels:
      app: SERVICE_NAME
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    metadata:
      labels:
        app: SERVICE_NAME
        tier: microservice
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/path: "/metrics"
        prometheus.io/port: "8000"
    spec:
      containers:
        - name: SERVICE_NAME
          image: zionix/SERVICE_NAME:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 8000
          resources:
            requests:
              cpu: 100m
              memory: 128Mi
            limits:
              cpu: 500m
              memory: 512Mi
          env:
            - name: POSTGRES_SERVER
              valueFrom:
                configMapKeyRef:
                  name: postgres-config
                  key: POSTGRES_HOST
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: SERVICE_NAME-secrets
                  key: POSTGRES_USER
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: SERVICE_NAME-secrets
                  key: POSTGRES_PASSWORD
            - name: POSTGRES_DB
              value: SERVICE_NAME_db
            - name: KAFKA_BOOTSTRAP_SERVERS
              value: kafka:9092
            - name: REDIS_URL
              value: redis://redis-client:6379/0
            - name: JWT_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: jwt-secrets
                  key: SECRET_KEY
            - name: ENVIRONMENT
              valueFrom:
                configMapKeyRef:
                  name: SERVICE_NAME-config
                  key: ENVIRONMENT
          livenessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 5
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: SERVICE_NAME
  labels:
    app: SERVICE_NAME
spec:
  selector:
    app: SERVICE_NAME
  ports:
    - port: 8000
      targetPort: 8000
      name: http
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: SERVICE_NAME-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: SERVICE_NAME
  minReplicas: 2
  maxReplicas: 10
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: 70
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: 80
---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: SERVICE_NAME-vs
spec:
  hosts:
    - "*"
  gateways:
    - zionix-gateway
  http:
    - match:
        - uri:
            prefix: /api/SERVICE_NAME
      route:
        - destination:
            host: SERVICE_NAME
            port:
              number: 8000
---
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: SERVICE_NAME-dr
spec:
  host: SERVICE_NAME
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    connectionPool:
      tcp:
        maxConnections: 100
      http:
        http2MaxRequests: 1000
        maxRequestsPerConnection: 10
    outlierDetection:
      consecutive5xxErrors: 5
      interval: 30s
      baseEjectionTime: 30s
