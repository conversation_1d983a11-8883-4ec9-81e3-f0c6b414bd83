#!/usr/bin/env python3

import requests
import json

def test_registration():
    """Test user registration endpoint"""
    
    # Test data
    registration_data = {
        "email": "<EMAIL>",
        "password": "TestPass123!",
        "confirm_password": "TestPass123!",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "action": "register"
    }
    
    url = "http://localhost:8002/api/v1/register"
    
    try:
        print("Testing registration endpoint...")
        print(f"URL: {url}")
        print(f"Data: {json.dumps(registration_data, indent=2)}")
        
        # Make the request
        response = requests.post(url, data=registration_data)
        
        print(f"\nResponse Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("Registration successful!")
            print(f"Response: {response.json()}")
        else:
            print("Registration failed!")
            print(f"Response Text: {response.text}")
            
            # Try to get JSON error details
            try:
                error_json = response.json()
                print(f"Error JSON: {json.dumps(error_json, indent=2)}")
            except:
                print("Could not parse error as J<PERSON><PERSON>")
                
    except Exception as e:
        print(f"Request failed with exception: {e}")

if __name__ == "__main__":
    test_registration()
