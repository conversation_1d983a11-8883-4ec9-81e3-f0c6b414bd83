#!/usr/bin/env python3

import requests
import json

def test_complete_token_flow():
    """Test login and token verification flow"""
    
    base_url = "http://localhost:8002/api/v1"
    
    # Step 1: Login to get a token
    print("=== Step 1: Login ===")
    login_data = {
        "username": "<EMAIL>",  # Using email as username
        "password": "TestPass123!"
    }
    
    login_response = requests.post(f"{base_url}/login", data=login_data)
    print(f"Login Status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print(f"Login failed: {login_response.text}")
        return
    
    login_result = login_response.json()
    token = login_result["refresh_token"]
    print(f"✅ Login successful!")
    print(f"Token: {token[:50]}...")
    
    # Step 2: Verify the token
    print("\n=== Step 2: Verify Token ===")
    verify_response = requests.post(f"{base_url}/verify-token?token={token}")
    print(f"Verify Status: {verify_response.status_code}")
    
    if verify_response.status_code == 200:
        user_info = verify_response.json()
        print("✅ Token verification successful!")
        print(f"User Info: {json.dumps(user_info, indent=2)}")
        
        # Check if login_token is stored
        if user_info.get("login_token"):
            print(f"✅ Login token stored in database: {user_info['login_token'][:50]}...")
        else:
            print("❌ Login token not found in user record")
            
    else:
        print(f"❌ Token verification failed: {verify_response.text}")

if __name__ == "__main__":
    test_complete_token_flow()
